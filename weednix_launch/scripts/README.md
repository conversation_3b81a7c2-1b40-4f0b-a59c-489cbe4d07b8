# 🤖 Robot Bringup Scripts

This directory contains scripts for automated robot startup and network configuration.

## 📋 Contents

- **`robot_bringup.sh`** - Main bringup script with monitoring and auto-restart
- **`setup_fixed_ip.sh`** - Network configuration script for static IP
- **`robot-bringup.service`** - Systemd service file for automatic startup
- **`README.md`** - This documentation

## 🚀 Quick Start

### 1. Basic Usage

```bash
# Start all services (ROS bringup, bridge, web server)
./robot_bringup.sh start

# Start with monitoring and auto-restart
./robot_bringup.sh monitor

# Check status
./robot_bringup.sh status

# Stop all services
./robot_bringup.sh stop
```

### 2. Set Up Fixed IP (Recommended)

```bash
# Interactive setup
./setup_fixed_ip.sh interactive

# Or specify directly
./setup_fixed_ip.sh ***********00 *********** wlan0
```

### 3. Install as System Service (Optional)

```bash
# Copy service file
sudo cp robot-bringup.service /etc/systemd/system/

# Enable and start service
sudo systemctl enable robot-bringup.service
sudo systemctl start robot-bringup.service

# Check service status
sudo systemctl status robot-bringup.service
```

## 📖 Detailed Documentation

### Robot Bringup Script (`robot_bringup.sh`)

#### Features
- ✅ Launches `device1_bringup.launch`
- ✅ Starts ROS bridge server (port 9090)
- ✅ Starts React web server (port 3000)
- ✅ Automatic IP detection and configuration
- ✅ Health monitoring with auto-restart
- ✅ Comprehensive logging
- ✅ Graceful shutdown handling

#### Commands

```bash
./robot_bringup.sh [COMMAND]

Commands:
  start     Start all services once
  stop      Stop all services
  restart   Restart all services
  status    Show status of all services
  monitor   Start with monitoring and auto-restart
  logs      Show recent log entries
  help      Show usage information
```

#### Configuration

The script automatically:
- Detects robot IP address
- Updates React app `.env` file
- Sources ROS environment
- Creates log directories
- Manages PID files

#### Logs

All logs are stored in `~/weednix_ws/src/logs/`:
- `robot_bringup.log` - Main script log
- `ros_bringup.log` - ROS launch output
- `ros_bridge.log` - ROS bridge output
- `web_server.log` - React server output

### Fixed IP Setup Script (`setup_fixed_ip.sh`)

#### Features
- ✅ Interactive configuration wizard
- ✅ Automatic network detection
- ✅ Netplan configuration generation
- ✅ Configuration backup
- ✅ WiFi and Ethernet support

#### Usage Examples

```bash
# Interactive mode (recommended)
./setup_fixed_ip.sh interactive

# Quick setup with defaults
./setup_fixed_ip.sh

# Specify IP only
./setup_fixed_ip.sh ***********00

# Full specification
./setup_fixed_ip.sh ***********00 *********** wlan0

# Show current network status
./setup_fixed_ip.sh show
```

#### What It Does

1. **Backs up** current network configuration
2. **Creates** netplan configuration file
3. **Applies** new network settings
4. **Provides** access URLs for web interface

### System Service (`robot-bringup.service`)

#### Features
- ✅ Automatic startup on boot
- ✅ Automatic restart on failure
- ✅ Proper user permissions
- ✅ Systemd integration
- ✅ Journal logging

#### Installation

```bash
# Copy service file
sudo cp robot-bringup.service /etc/systemd/system/

# Reload systemd
sudo systemctl daemon-reload

# Enable service (start on boot)
sudo systemctl enable robot-bringup.service

# Start service now
sudo systemctl start robot-bringup.service
```

#### Management

```bash
# Check status
sudo systemctl status robot-bringup.service

# View logs
sudo journalctl -u robot-bringup.service -f

# Stop service
sudo systemctl stop robot-bringup.service

# Disable service (don't start on boot)
sudo systemctl disable robot-bringup.service
```

## 🔧 Configuration

### Environment Variables

The scripts use these environment variables:
- `ROS_MASTER_URI` - ROS master location (default: http://localhost:11311)
- `ROS_HOSTNAME` - ROS hostname (default: localhost)
- `HOST` - Web server host (set to 0.0.0.0 for network access)
- `PORT` - Web server port (default: 3000)

### Network Ports

- **3000** - React web interface
- **9090** - ROS bridge WebSocket
- **11311** - ROS master

### File Locations

- **Scripts**: `~/weednix_ws/src/weednix_launch/scripts/`
- **Logs**: `~/weednix_ws/src/logs/`
- **PID files**: `/tmp/robot_bringup/`
- **React app**: `~/weednix_ws/src/react-ros-app/`

## 🛠️ Troubleshooting

### Common Issues

1. **Port already in use**
   ```bash
   # Check what's using the port
   sudo ss -tlnp | grep :3000
   sudo ss -tlnp | grep :9090
   
   # Kill processes if needed
   ./robot_bringup.sh stop
   ```

2. **ROS environment not found**
   ```bash
   # Check ROS installation
   which roscore
   source /opt/ros/noetic/setup.bash
   ```

3. **Node.js/npm not found**
   ```bash
   # Check Node.js version (should be 18+)
   node --version
   npm --version
   ```

4. **Network access issues**
   ```bash
   # Check firewall
   sudo ufw status
   sudo ufw allow 3000
   sudo ufw allow 9090
   
   # Check IP configuration
   ./setup_fixed_ip.sh show
   ```

### Log Analysis

```bash
# View all recent logs
./robot_bringup.sh logs

# Monitor logs in real-time
tail -f ~/weednix_ws/src/logs/robot_bringup.log

# Check specific service logs
tail -f ~/weednix_ws/src/logs/ros_bringup.log
tail -f ~/weednix_ws/src/logs/web_server.log
```

### Service Debugging

```bash
# Check service status
sudo systemctl status robot-bringup.service

# View service logs
sudo journalctl -u robot-bringup.service --since "1 hour ago"

# Restart service
sudo systemctl restart robot-bringup.service
```

## 📱 Access URLs

After successful startup, access your robot at:

- **Web Interface**: `http://[ROBOT_IP]:3000`
- **ROS Bridge**: `ws://[ROBOT_IP]:9090`

Replace `[ROBOT_IP]` with your robot's actual IP address.

## 🆘 Support

If you encounter issues:

1. Check the troubleshooting section above
2. Review log files for error messages
3. Ensure all prerequisites are installed
4. Verify network connectivity
5. Check firewall settings

For additional help, run:
```bash
./robot_bringup.sh help
./setup_fixed_ip.sh help
```
