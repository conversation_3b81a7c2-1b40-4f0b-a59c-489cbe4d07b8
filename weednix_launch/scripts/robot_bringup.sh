#!/bin/bash

# =============================================================================
# Robot Bringup Script
# =============================================================================
# This script launches the device1_bringup launch file and manages the web
# server automatically with error handling and restart capabilities.
#
# Usage:
#   ./robot_bringup.sh [start|stop|restart|status]
#
# Features:
# - Launches ROS device1_bringup.launch
# - Starts ROS bridge server (port 9090)
# - Starts React web server (port 3000)
# - Automatic restart on failures
# - Logging and status monitoring
# - Graceful shutdown handling
# =============================================================================

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
WORKSPACE_DIR="$(cd "$SCRIPT_DIR/../../.." && pwd)"
REACT_APP_DIR="$WORKSPACE_DIR/src/react-ros-app"
LOG_DIR="$WORKSPACE_DIR/logs"
PID_DIR="/tmp/robot_bringup"

# Log files
ROS_LOG="$LOG_DIR/ros_bringup.log"
BRIDGE_LOG="$LOG_DIR/ros_bridge.log"
WEB_LOG="$LOG_DIR/web_server.log"
MAIN_LOG="$LOG_DIR/robot_bringup.log"

# PID files
ROS_PID="$PID_DIR/ros_bringup.pid"
BRIDGE_PID="$PID_DIR/ros_bridge.pid"
WEB_PID="$PID_DIR/web_server.pid"

# Configuration
MAX_RESTART_ATTEMPTS=5
RESTART_DELAY=10
HEALTH_CHECK_INTERVAL=30

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# =============================================================================
# Utility Functions
# =============================================================================

log() {
    echo -e "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$MAIN_LOG"
}

log_error() {
    echo -e "$(date '+%Y-%m-%d %H:%M:%S') - ${RED}ERROR: $1${NC}" | tee -a "$MAIN_LOG"
}

log_success() {
    echo -e "$(date '+%Y-%m-%d %H:%M:%S') - ${GREEN}SUCCESS: $1${NC}" | tee -a "$MAIN_LOG"
}

log_warning() {
    echo -e "$(date '+%Y-%m-%d %H:%M:%S') - ${YELLOW}WARNING: $1${NC}" | tee -a "$MAIN_LOG"
}

log_info() {
    echo -e "$(date '+%Y-%m-%d %H:%M:%S') - ${BLUE}INFO: $1${NC}" | tee -a "$MAIN_LOG"
}

# Create necessary directories
setup_directories() {
    mkdir -p "$LOG_DIR" "$PID_DIR"
    log_info "Created log directory: $LOG_DIR"
    log_info "Created PID directory: $PID_DIR"
}

# Get robot IP address
get_robot_ip() {
    # Try hostname -I first (most reliable on Ubuntu)
    IP=$(hostname -I | awk '{print $1}' 2>/dev/null)
    
    # Fallback to ip route
    if [ -z "$IP" ]; then
        IP=$(ip route get 1 2>/dev/null | awk '{print $7}' | head -1)
    fi
    
    # Fallback to ifconfig
    if [ -z "$IP" ]; then
        IP=$(ifconfig 2>/dev/null | grep -Eo 'inet (addr:)?([0-9]*\.){3}[0-9]*' | grep -Eo '([0-9]*\.){3}[0-9]*' | grep -v '127.0.0.1' | head -1)
    fi
    
    echo "$IP"
}

# Check if a process is running by PID file
is_process_running() {
    local pid_file="$1"
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            return 0
        else
            rm -f "$pid_file"
            return 1
        fi
    fi
    return 1
}

# Check if a port is in use
is_port_in_use() {
    local port="$1"
    ss -tlnp | grep -q ":$port "
}

# Kill process by PID file
kill_process() {
    local pid_file="$1"
    local process_name="$2"
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            log_info "Stopping $process_name (PID: $pid)"
            kill -TERM "$pid"
            
            # Wait for graceful shutdown
            local count=0
            while kill -0 "$pid" 2>/dev/null && [ $count -lt 10 ]; do
                sleep 1
                count=$((count + 1))
            done
            
            # Force kill if still running
            if kill -0 "$pid" 2>/dev/null; then
                log_warning "Force killing $process_name (PID: $pid)"
                kill -KILL "$pid"
            fi
            
            log_success "$process_name stopped"
        fi
        rm -f "$pid_file"
    fi
}

# Source ROS environment
source_ros_environment() {
    # First source the base ROS installation
    if [ -f "/opt/ros/noetic/setup.bash" ]; then
        source "/opt/ros/noetic/setup.bash"
        log_info "Sourced ROS Noetic: /opt/ros/noetic/setup.bash"
    else
        log_error "Could not find ROS Noetic installation"
        return 1
    fi

    # Then source the workspace if it exists
    if [ -f "$WORKSPACE_DIR/devel/setup.bash" ]; then
        source "$WORKSPACE_DIR/devel/setup.bash"
        log_info "Sourced ROS workspace: $WORKSPACE_DIR/devel/setup.bash"
    else
        log_warning "Workspace devel/setup.bash not found, trying to build workspace..."
        cd "$WORKSPACE_DIR"
        if catkin_make; then
            source "$WORKSPACE_DIR/devel/setup.bash"
            log_success "Workspace built and sourced successfully"
        else
            log_error "Failed to build workspace"
            return 1
        fi
    fi

    return 0
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if ROS is available
    if ! command -v roscore &> /dev/null; then
        log_error "ROS is not installed or not in PATH"
        return 1
    fi
    
    # Check if Node.js is available
    if ! command -v node &> /dev/null; then
        log_error "Node.js is not installed or not in PATH"
        return 1
    fi
    
    # Check if npm is available
    if ! command -v npm &> /dev/null; then
        log_error "npm is not installed or not in PATH"
        return 1
    fi
    
    # Check if React app directory exists
    if [ ! -d "$REACT_APP_DIR" ]; then
        log_error "React app directory not found: $REACT_APP_DIR"
        return 1
    fi
    
    # Check if package.json exists
    if [ ! -f "$REACT_APP_DIR/package.json" ]; then
        log_error "package.json not found in: $REACT_APP_DIR"
        return 1
    fi
    
    # Check if node_modules exists
    if [ ! -d "$REACT_APP_DIR/node_modules" ]; then
        log_warning "node_modules not found, running npm install..."
        cd "$REACT_APP_DIR"
        npm install
        if [ $? -ne 0 ]; then
            log_error "npm install failed"
            return 1
        fi
    fi
    
    log_success "All prerequisites checked"
    return 0
}

# Update React app configuration with current IP
update_react_config() {
    local robot_ip="$1"
    log_info "Updating React app configuration with IP: $robot_ip"
    
    cd "$REACT_APP_DIR"
    
    # Create or update .env file
    cat > .env << EOF
# React App Configuration for Network Access
HOST=0.0.0.0
PORT=3000

# ROS Bridge Configuration
REACT_APP_DEFAULT_ROS_URL=ws://$robot_ip:9090
REACT_APP_ROBOT_IP=$robot_ip

# Network Configuration
REACT_APP_SHOW_NETWORK_INFO=true
EOF
    
    log_success "Updated .env file with robot IP: $robot_ip"
}

# =============================================================================
# Core Functions
# =============================================================================

# Check if roscore is running
is_roscore_running() {
    pgrep -f "roscore" > /dev/null 2>&1
}

# Start roscore if not running
ensure_roscore() {
    if is_roscore_running; then
        log_info "ROS master is already running"
        return 0
    fi

    log_info "Starting ROS master (roscore)..."

    # Source ROS environment
    if ! source_ros_environment; then
        return 1
    fi

    # Start roscore in background
    nohup roscore > "$LOG_DIR/roscore.log" 2>&1 &
    local roscore_pid=$!

    # Wait for roscore to start
    local count=0
    while [ $count -lt 10 ]; do
        if is_roscore_running; then
            log_success "ROS master started successfully"
            return 0
        fi
        sleep 1
        count=$((count + 1))
    done

    log_error "Failed to start ROS master"
    return 1
}

# Start ROS bringup launch file
start_ros_bringup() {
    log_info "Starting ROS device1_bringup..."

    if is_process_running "$ROS_PID"; then
        log_warning "ROS bringup is already running"
        return 0
    fi

    # Ensure roscore is running first
    if ! ensure_roscore; then
        log_error "Cannot start ROS bringup without ROS master"
        return 1
    fi

    # Source ROS environment
    if ! source_ros_environment; then
        return 1
    fi

    # Check if hardware devices exist
    local hardware_available="true"
    if [ ! -e "/dev/ttyUSB_arduino" ] || [ ! -e "/dev/ttyUSB_imu_bno055" ]; then
        hardware_available="false"
        log_warning "Hardware devices not detected, running in software-only mode"
        log_info "Missing devices: $([ ! -e "/dev/ttyUSB_arduino" ] && echo "/dev/ttyUSB_arduino "; [ ! -e "/dev/ttyUSB_imu_bno055" ] && echo "/dev/ttyUSB_imu_bno055")"
    fi

    # Change to workspace directory and start roslaunch in background
    cd "$WORKSPACE_DIR"

    # Create a temporary script to run the launch command
    local launch_script="/tmp/robot_bringup_launch.sh"
    cat > "$launch_script" << EOF
#!/bin/bash
cd "$WORKSPACE_DIR"
source /opt/ros/noetic/setup.bash
source devel/setup.bash
exec roslaunch weednix_launch device1_bringup.launch hardware:=$hardware_available
EOF
    chmod +x "$launch_script"

    # Run the launch script in background
    nohup "$launch_script" > "$ROS_LOG" 2>&1 &
    local pid=$!
    echo $pid > "$ROS_PID"

    # Wait a moment and check if it's still running
    sleep 5
    if kill -0 "$pid" 2>/dev/null; then
        log_success "ROS bringup started (PID: $pid)"
        return 0
    else
        log_error "ROS bringup failed to start"
        log_info "Check ROS log for details: $ROS_LOG"
        rm -f "$ROS_PID"
        return 1
    fi
}

# Start ROS bridge server
start_ros_bridge() {
    log_info "Starting ROS bridge server..."

    if is_process_running "$BRIDGE_PID"; then
        log_warning "ROS bridge is already running"
        return 0
    fi

    # Check if port 9090 is already in use
    if is_port_in_use 9090; then
        log_error "Port 9090 is already in use"
        return 1
    fi

    # Source ROS environment
    if ! source_ros_environment; then
        return 1
    fi

    # Start rosbridge in background
    cd "$WORKSPACE_DIR"

    # Create a temporary script to run the bridge command
    local bridge_script="/tmp/robot_bridge_launch.sh"
    cat > "$bridge_script" << EOF
#!/bin/bash
cd "$WORKSPACE_DIR"
source /opt/ros/noetic/setup.bash
source devel/setup.bash
exec roslaunch rosbridge_server rosbridge_websocket.launch
EOF
    chmod +x "$bridge_script"

    # Run the bridge script in background
    nohup "$bridge_script" > "$BRIDGE_LOG" 2>&1 &
    local pid=$!
    echo $pid > "$BRIDGE_PID"

    # Wait a moment and check if it's still running
    sleep 5
    if kill -0 "$pid" 2>/dev/null; then
        log_success "ROS bridge started (PID: $pid)"
        return 0
    else
        log_error "ROS bridge failed to start"
        rm -f "$BRIDGE_PID"
        return 1
    fi
}

# Start React web server
start_web_server() {
    log_info "Starting React web server..."

    if is_process_running "$WEB_PID"; then
        log_warning "Web server is already running"
        return 0
    fi

    # Check if port 3000 is already in use
    if is_port_in_use 3000; then
        log_warning "Port 3000 is already in use, attempting to free it..."

        # Try to kill existing React processes
        pkill -f "react-scripts start" 2>/dev/null || true
        pkill -f "npm start" 2>/dev/null || true

        # Wait a moment for processes to die
        sleep 3

        # Check again
        if is_port_in_use 3000; then
            log_error "Port 3000 is still in use after cleanup attempt"
            log_info "Try running: ./robot_bringup.sh stop"
            log_info "Or manually kill processes: sudo ss -tlnp | grep :3000"
            return 1
        else
            log_success "Port 3000 freed successfully"
        fi
    fi

    # Change to React app directory
    cd "$REACT_APP_DIR"

    # Start npm in background
    nohup npm start > "$WEB_LOG" 2>&1 &
    local pid=$!
    echo $pid > "$WEB_PID"

    # Wait for the server to start (React takes longer)
    log_info "Waiting for React server to start..."
    local count=0
    while [ $count -lt 30 ]; do
        if is_port_in_use 3000; then
            log_success "Web server started (PID: $pid)"
            return 0
        fi
        sleep 2
        count=$((count + 1))
    done

    # Check if process is still running
    if kill -0 "$pid" 2>/dev/null; then
        log_warning "Web server process is running but port 3000 not accessible yet"
        return 0
    else
        log_error "Web server failed to start"
        rm -f "$WEB_PID"
        return 1
    fi
}

# Stop all services
stop_all_services() {
    log_info "Stopping all services..."

    kill_process "$WEB_PID" "Web server"
    kill_process "$BRIDGE_PID" "ROS bridge"
    kill_process "$ROS_PID" "ROS bringup"

    # Stop roscore if it's running
    if is_roscore_running; then
        log_info "Stopping ROS master (roscore)..."
        pkill -f "roscore" || true
        sleep 2
        if is_roscore_running; then
            log_warning "Force killing roscore..."
            pkill -9 -f "roscore" || true
        fi
        log_success "ROS master stopped"
    fi

    # Clean up any remaining processes on our ports
    if is_port_in_use 3000; then
        log_warning "Cleaning up remaining processes on port 3000"
        pkill -f "react-scripts start" || true
    fi

    if is_port_in_use 9090; then
        log_warning "Cleaning up remaining processes on port 9090"
        pkill -f "rosbridge" || true
    fi

    log_success "All services stopped"
}

# Check health of all services
check_health() {
    local all_healthy=true

    # Check ROS master
    if is_roscore_running; then
        log_info "✅ ROS master is running"
    else
        log_error "❌ ROS master is not running"
        all_healthy=false
    fi

    # Check ROS bringup
    if is_process_running "$ROS_PID"; then
        log_info "✅ ROS bringup is running"
    else
        log_error "❌ ROS bringup is not running"
        all_healthy=false
    fi

    # Check ROS bridge
    if is_process_running "$BRIDGE_PID" && is_port_in_use 9090; then
        log_info "✅ ROS bridge is running (port 9090)"
    else
        log_error "❌ ROS bridge is not running or port 9090 not accessible"
        all_healthy=false
    fi

    # Check web server
    if is_process_running "$WEB_PID" && is_port_in_use 3000; then
        log_info "✅ Web server is running (port 3000)"
    else
        log_error "❌ Web server is not running or port 3000 not accessible"
        all_healthy=false
    fi

    if $all_healthy; then
        return 0
    else
        return 1
    fi
}

# Start all services
start_all_services() {
    log_info "Starting robot bringup sequence..."

    # Get robot IP
    local robot_ip=$(get_robot_ip)
    if [ -z "$robot_ip" ]; then
        log_error "Could not determine robot IP address"
        return 1
    fi

    log_info "Robot IP: $robot_ip"

    # Update React configuration
    update_react_config "$robot_ip"

    # Start services in order
    if ! start_ros_bringup; then
        log_error "Failed to start ROS bringup"
        return 1
    fi

    # Wait for ROS to be ready
    sleep 10

    if ! start_ros_bridge; then
        log_error "Failed to start ROS bridge"
        stop_all_services
        return 1
    fi

    if ! start_web_server; then
        log_error "Failed to start web server"
        stop_all_services
        return 1
    fi

    log_success "All services started successfully!"
    log_info "📱 Access URLs:"
    log_info "  Web Interface: http://$robot_ip:3000"
    log_info "  ROS Bridge:    ws://$robot_ip:9090"

    return 0
}

# =============================================================================
# Monitoring and Restart Logic
# =============================================================================

# Monitor services and restart if needed
monitor_services() {
    log_info "Starting service monitoring..."
    local restart_count=0

    while true; do
        sleep "$HEALTH_CHECK_INTERVAL"

        if ! check_health; then
            log_warning "Health check failed, attempting restart..."
            restart_count=$((restart_count + 1))

            if [ $restart_count -gt $MAX_RESTART_ATTEMPTS ]; then
                log_error "Maximum restart attempts ($MAX_RESTART_ATTEMPTS) reached. Stopping monitoring."
                break
            fi

            log_info "Restart attempt $restart_count/$MAX_RESTART_ATTEMPTS"

            # Stop all services
            stop_all_services

            # Wait before restart
            sleep "$RESTART_DELAY"

            # Try to start again
            if start_all_services; then
                log_success "Services restarted successfully"
                restart_count=0  # Reset counter on successful restart
            else
                log_error "Failed to restart services"
            fi
        else
            # Reset restart counter if everything is healthy
            if [ $restart_count -gt 0 ]; then
                log_info "All services healthy, resetting restart counter"
                restart_count=0
            fi
        fi
    done
}

# Show status of all services
show_status() {
    echo "=============================================="
    echo "🤖 Robot Bringup Status"
    echo "=============================================="
    echo ""

    local robot_ip=$(get_robot_ip)
    echo "📡 Robot IP: ${robot_ip:-'Unknown'}"
    echo ""

    echo "📊 Service Status:"
    echo "==================="

    # ROS master status
    if is_roscore_running; then
        echo "✅ ROS Master: Running"
    else
        echo "❌ ROS Master: Not running"
    fi

    # ROS bringup status
    if is_process_running "$ROS_PID"; then
        local pid=$(cat "$ROS_PID")
        echo "✅ ROS Bringup: Running (PID: $pid)"
    else
        echo "❌ ROS Bringup: Not running"
    fi

    # ROS bridge status
    if is_process_running "$BRIDGE_PID"; then
        local pid=$(cat "$BRIDGE_PID")
        if is_port_in_use 9090; then
            echo "✅ ROS Bridge: Running (PID: $pid, Port: 9090)"
        else
            echo "⚠️  ROS Bridge: Process running but port 9090 not accessible"
        fi
    else
        echo "❌ ROS Bridge: Not running"
    fi

    # Web server status
    if is_process_running "$WEB_PID"; then
        local pid=$(cat "$WEB_PID")
        if is_port_in_use 3000; then
            echo "✅ Web Server: Running (PID: $pid, Port: 3000)"
        else
            echo "⚠️  Web Server: Process running but port 3000 not accessible"
        fi
    else
        echo "❌ Web Server: Not running"
    fi

    echo ""
    echo "🌐 Access URLs:"
    echo "==============="
    if [ -n "$robot_ip" ]; then
        echo "  Web Interface: http://$robot_ip:3000"
        echo "  ROS Bridge:    ws://$robot_ip:9090"
    else
        echo "  Cannot determine access URLs (no IP detected)"
    fi

    echo ""
    echo "📁 Log Files:"
    echo "============="
    echo "  Main Log:     $MAIN_LOG"
    echo "  ROS Log:      $ROS_LOG"
    echo "  Bridge Log:   $BRIDGE_LOG"
    echo "  Web Log:      $WEB_LOG"

    echo ""
}

# =============================================================================
# Signal Handlers
# =============================================================================

# Handle shutdown signals
cleanup_and_exit() {
    log_info "Received shutdown signal, cleaning up..."
    stop_all_services
    exit 0
}

# Set up signal handlers
trap cleanup_and_exit SIGTERM SIGINT

# =============================================================================
# Main Command Handling
# =============================================================================

# Show usage information
show_usage() {
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start     Start all services (ROS bringup, bridge, web server)"
    echo "  stop      Stop all services"
    echo "  restart   Restart all services"
    echo "  status    Show status of all services"
    echo "  monitor   Start services and monitor with auto-restart"
    echo "  logs      Show recent log entries"
    echo "  cleanup   Force cleanup all processes and ports"
    echo ""
    echo "Examples:"
    echo "  $0 start          # Start all services once"
    echo "  $0 monitor        # Start and monitor with auto-restart"
    echo "  $0 status         # Check current status"
    echo "  $0 stop           # Stop everything"
    echo ""
}

# Show recent logs
show_logs() {
    echo "=============================================="
    echo "📋 Recent Log Entries"
    echo "=============================================="
    echo ""

    if [ -f "$MAIN_LOG" ]; then
        echo "🔍 Main Log (last 20 lines):"
        echo "=============================="
        tail -20 "$MAIN_LOG"
        echo ""
    fi

    if [ -f "$ROS_LOG" ]; then
        echo "🤖 ROS Log (last 10 lines):"
        echo "============================"
        tail -10 "$ROS_LOG"
        echo ""
    fi

    if [ -f "$BRIDGE_LOG" ]; then
        echo "🌉 Bridge Log (last 10 lines):"
        echo "==============================="
        tail -10 "$BRIDGE_LOG"
        echo ""
    fi

    if [ -f "$WEB_LOG" ]; then
        echo "🌐 Web Log (last 10 lines):"
        echo "============================"
        tail -10 "$WEB_LOG"
        echo ""
    fi
}

# =============================================================================
# Main Function
# =============================================================================

main() {
    # Initialize
    setup_directories

    # Check prerequisites
    if ! check_prerequisites; then
        log_error "Prerequisites check failed"
        exit 1
    fi

    # Parse command line arguments
    case "${1:-start}" in
        "start")
            log_info "Starting robot bringup..."
            if start_all_services; then
                log_success "Robot bringup completed successfully!"
                show_status
            else
                log_error "Robot bringup failed"
                exit 1
            fi
            ;;

        "stop")
            log_info "Stopping robot services..."
            stop_all_services
            ;;

        "restart")
            log_info "Restarting robot services..."
            stop_all_services
            sleep 5
            if start_all_services; then
                log_success "Robot restart completed successfully!"
                show_status
            else
                log_error "Robot restart failed"
                exit 1
            fi
            ;;

        "status")
            show_status
            ;;

        "monitor")
            log_info "Starting robot bringup with monitoring..."
            if start_all_services; then
                log_success "Robot bringup completed successfully!"
                show_status
                log_info "Starting monitoring mode (Ctrl+C to stop)..."
                monitor_services
            else
                log_error "Robot bringup failed"
                exit 1
            fi
            ;;

        "logs")
            show_logs
            ;;

        "cleanup")
            log_info "Cleaning up all robot-related processes..."
            stop_all_services

            # Additional cleanup
            pkill -f "react-scripts" 2>/dev/null || true
            pkill -f "npm.*start" 2>/dev/null || true
            pkill -f "rosbridge" 2>/dev/null || true
            pkill -f "roslaunch.*weednix" 2>/dev/null || true

            # Clean up ports
            if is_port_in_use 3000; then
                log_warning "Force cleaning port 3000..."
                fuser -k 3000/tcp 2>/dev/null || true
            fi

            if is_port_in_use 9090; then
                log_warning "Force cleaning port 9090..."
                fuser -k 9090/tcp 2>/dev/null || true
            fi

            log_success "Cleanup completed"
            ;;

        "help"|"-h"|"--help")
            show_usage
            ;;

        *)
            echo "Unknown command: $1"
            echo ""
            show_usage
            exit 1
            ;;
    esac
}

# =============================================================================
# Script Entry Point
# =============================================================================

# Check if script is being sourced or executed
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    # Script is being executed directly
    main "$@"
fi
