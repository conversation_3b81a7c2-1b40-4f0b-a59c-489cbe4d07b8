#!/bin/bash

# =============================================================================
# Fixed IP Setup Script
# =============================================================================
# This script helps configure a static IP address for the robot to ensure
# consistent network access for the web interface and ROS bridge.
#
# Usage:
#   ./setup_fixed_ip.sh [IP_ADDRESS] [GATEWAY] [INTERFACE]
#
# Example:
#   ./setup_fixed_ip.sh ***********00 *********** wlan0
# =============================================================================

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
DEFAULT_IP="***********00"
DEFAULT_GATEWAY="***********"
DEFAULT_INTERFACE=""

log_info() {
    echo -e "${BLUE}INFO: $1${NC}"
}

log_success() {
    echo -e "${GREEN}SUCCESS: $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}WARNING: $1${NC}"
}

log_error() {
    echo -e "${RED}ERROR: $1${NC}"
}

# Get current network interface
get_current_interface() {
    # Get the interface with the default route
    ip route | grep default | awk '{print $5}' | head -1
}

# Get current IP
get_current_ip() {
    local interface="$1"
    ip addr show "$interface" 2>/dev/null | grep 'inet ' | awk '{print $2}' | cut -d'/' -f1 | head -1
}

# Get current gateway
get_current_gateway() {
    ip route | grep default | awk '{print $3}' | head -1
}

# Show current network configuration
show_current_config() {
    echo "=============================================="
    echo "🌐 Current Network Configuration"
    echo "=============================================="
    echo ""
    
    local interface=$(get_current_interface)
    local current_ip=$(get_current_ip "$interface")
    local current_gateway=$(get_current_gateway)
    
    echo "📡 Active Interface: ${interface:-'None detected'}"
    echo "🏠 Current IP: ${current_ip:-'None detected'}"
    echo "🚪 Current Gateway: ${current_gateway:-'None detected'}"
    echo ""
    
    # Show all interfaces
    echo "📋 All Network Interfaces:"
    echo "=========================="
    ip addr show | grep -E '^[0-9]+:' | awk '{print $2}' | sed 's/://' | while read iface; do
        local ip=$(get_current_ip "$iface")
        echo "  $iface: ${ip:-'No IP'}"
    done
    echo ""
}

# Backup current network configuration
backup_network_config() {
    local backup_dir="/etc/netplan/backup_$(date +%Y%m%d_%H%M%S)"
    
    log_info "Creating backup of current network configuration..."
    
    if [ -d "/etc/netplan" ]; then
        sudo mkdir -p "$backup_dir"
        sudo cp /etc/netplan/*.yaml "$backup_dir/" 2>/dev/null || true
        log_success "Backup created at: $backup_dir"
        return 0
    else
        log_warning "Netplan directory not found, skipping backup"
        return 1
    fi
}

# Create netplan configuration for static IP
create_netplan_config() {
    local ip_address="$1"
    local gateway="$2"
    local interface="$3"
    
    local config_file="/etc/netplan/01-robot-static-ip.yaml"
    
    log_info "Creating netplan configuration for static IP..."
    
    # Determine if it's WiFi or Ethernet
    local connection_type="ethernets"
    if [[ "$interface" == wlan* ]]; then
        connection_type="wifis"
    fi
    
    # Create the netplan configuration
    sudo tee "$config_file" > /dev/null << EOF
# Robot Static IP Configuration
# Generated by setup_fixed_ip.sh on $(date)
network:
  version: 2
  renderer: networkd
  $connection_type:
    $interface:
      dhcp4: false
      addresses:
        - $ip_address/24
      gateway4: $gateway
      nameservers:
        addresses:
          - *******
          - *******
          - $gateway
EOF

    if [[ "$connection_type" == "wifis" ]]; then
        # For WiFi, we need to add access point configuration
        log_warning "WiFi interface detected. You'll need to manually add WiFi credentials."
        log_info "Edit $config_file and add your WiFi network details:"
        echo ""
        echo "Example WiFi configuration to add:"
        echo "      access-points:"
        echo "        \"YOUR_WIFI_NAME\":"
        echo "          password: \"YOUR_WIFI_PASSWORD\""
        echo ""
    fi
    
    log_success "Netplan configuration created: $config_file"
}

# Apply network configuration
apply_network_config() {
    log_info "Applying network configuration..."
    
    # Test the configuration first
    if sudo netplan try --timeout=30; then
        log_success "Network configuration applied successfully"
        return 0
    else
        log_error "Failed to apply network configuration"
        return 1
    fi
}

# Show usage
show_usage() {
    echo "Usage: $0 [IP_ADDRESS] [GATEWAY] [INTERFACE]"
    echo ""
    echo "Parameters:"
    echo "  IP_ADDRESS  Static IP address to assign (default: $DEFAULT_IP)"
    echo "  GATEWAY     Gateway IP address (default: $DEFAULT_GATEWAY)"
    echo "  INTERFACE   Network interface name (default: auto-detect)"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Use defaults with auto-detection"
    echo "  $0 ***********00                     # Set IP, auto-detect gateway and interface"
    echo "  $0 ***********00 ***********         # Set IP and gateway, auto-detect interface"
    echo "  $0 ***********00 *********** wlan0   # Set all parameters"
    echo ""
    echo "Interactive mode:"
    echo "  $0 interactive                        # Interactive configuration"
    echo ""
}

# Interactive configuration
interactive_config() {
    echo "=============================================="
    echo "🔧 Interactive Network Configuration"
    echo "=============================================="
    echo ""
    
    show_current_config
    
    # Get interface
    local current_interface=$(get_current_interface)
    echo "Available interfaces:"
    ip addr show | grep -E '^[0-9]+:' | awk '{print $2}' | sed 's/://' | grep -v lo
    echo ""
    read -p "Enter interface name (default: $current_interface): " interface
    interface=${interface:-$current_interface}
    
    # Get IP address
    local current_ip=$(get_current_ip "$interface")
    read -p "Enter static IP address (default: $DEFAULT_IP): " ip_address
    ip_address=${ip_address:-$DEFAULT_IP}
    
    # Get gateway
    local current_gateway=$(get_current_gateway)
    read -p "Enter gateway IP (default: ${current_gateway:-$DEFAULT_GATEWAY}): " gateway
    gateway=${gateway:-${current_gateway:-$DEFAULT_GATEWAY}}
    
    echo ""
    echo "Configuration Summary:"
    echo "====================="
    echo "Interface: $interface"
    echo "IP Address: $ip_address"
    echo "Gateway: $gateway"
    echo ""
    
    read -p "Apply this configuration? (y/N): " confirm
    if [[ "$confirm" =~ ^[Yy]$ ]]; then
        setup_static_ip "$ip_address" "$gateway" "$interface"
    else
        log_info "Configuration cancelled"
    fi
}

# Main setup function
setup_static_ip() {
    local ip_address="$1"
    local gateway="$2"
    local interface="$3"
    
    log_info "Setting up static IP configuration..."
    log_info "IP: $ip_address, Gateway: $gateway, Interface: $interface"
    
    # Backup current configuration
    backup_network_config
    
    # Create new configuration
    create_netplan_config "$ip_address" "$gateway" "$interface"
    
    # Apply configuration
    if apply_network_config; then
        log_success "Static IP configuration completed!"
        echo ""
        echo "🎉 Network Configuration Applied Successfully!"
        echo "=============================================="
        echo "📡 Static IP: $ip_address"
        echo "🚪 Gateway: $gateway"
        echo "🔌 Interface: $interface"
        echo ""
        echo "🌐 Your robot will now be accessible at:"
        echo "  Web Interface: http://$ip_address:3000"
        echo "  ROS Bridge:    ws://$ip_address:9090"
        echo ""
        log_warning "Please reboot your system to ensure all changes take effect"
        echo "sudo reboot"
    else
        log_error "Failed to apply static IP configuration"
        return 1
    fi
}

# Main function
main() {
    # Check if running as root for netplan operations
    if [[ $EUID -eq 0 ]]; then
        log_error "This script should not be run as root. Use sudo when needed."
        exit 1
    fi
    
    # Check if netplan is available
    if ! command -v netplan &> /dev/null; then
        log_error "Netplan is not available. This script is designed for Ubuntu 18.04+ with netplan."
        exit 1
    fi
    
    case "${1:-auto}" in
        "interactive")
            interactive_config
            ;;
        "show"|"status")
            show_current_config
            ;;
        "help"|"-h"|"--help")
            show_usage
            ;;
        *)
            # Parse arguments
            local ip_address="${1:-$DEFAULT_IP}"
            local gateway="${2:-$(get_current_gateway)}"
            local interface="${3:-$(get_current_interface)}"
            
            # Use defaults if detection failed
            gateway="${gateway:-$DEFAULT_GATEWAY}"
            
            if [ -z "$interface" ]; then
                log_error "Could not detect network interface. Please specify manually."
                show_usage
                exit 1
            fi
            
            setup_static_ip "$ip_address" "$gateway" "$interface"
            ;;
    esac
}

# Run main function
main "$@"
