#!/bin/bash

# =============================================================================
# Robot Bringup Installation Script
# =============================================================================
# This script sets up the robot bringup system with all necessary components.
#
# Usage:
#   ./install.sh [--with-service] [--with-fixed-ip]
#
# Options:
#   --with-service    Install as systemd service for auto-startup
#   --with-fixed-ip   Configure static IP address
# =============================================================================

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
WORKSPACE_DIR="$(cd "$SCRIPT_DIR/../../.." && pwd)"

log_info() {
    echo -e "${BLUE}INFO: $1${NC}"
}

log_success() {
    echo -e "${GREEN}SUCCESS: $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}WARNING: $1${NC}"
}

log_error() {
    echo -e "${RED}ERROR: $1${NC}"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    local missing_deps=()
    
    # Check ROS
    if ! command -v roscore &> /dev/null; then
        missing_deps+=("ROS (roscore not found)")
    fi
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        missing_deps+=("Node.js")
    else
        local node_version=$(node --version | sed 's/v//')
        local major_version=$(echo $node_version | cut -d. -f1)
        if [ "$major_version" -lt 18 ]; then
            missing_deps+=("Node.js 18+ (current: $node_version)")
        fi
    fi
    
    # Check npm
    if ! command -v npm &> /dev/null; then
        missing_deps+=("npm")
    fi
    
    # Check rosbridge
    if ! rospack find rosbridge_server &> /dev/null; then
        missing_deps+=("rosbridge_server package")
    fi
    
    if [ ${#missing_deps[@]} -gt 0 ]; then
        log_error "Missing prerequisites:"
        for dep in "${missing_deps[@]}"; do
            echo "  - $dep"
        done
        echo ""
        log_info "To install missing dependencies:"
        echo "  # For ROS and rosbridge:"
        echo "  sudo apt update"
        echo "  sudo apt install ros-noetic-rosbridge-suite"
        echo ""
        echo "  # For Node.js 18+:"
        echo "  curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -"
        echo "  sudo apt-get install nodejs -y"
        echo ""
        return 1
    fi
    
    log_success "All prerequisites are satisfied"
    return 0
}

# Install React app dependencies
install_react_deps() {
    log_info "Installing React app dependencies..."
    
    local react_dir="$WORKSPACE_DIR/src/react-ros-app"
    
    if [ ! -d "$react_dir" ]; then
        log_error "React app directory not found: $react_dir"
        return 1
    fi
    
    cd "$react_dir"
    
    if [ ! -f "package.json" ]; then
        log_error "package.json not found in React app directory"
        return 1
    fi
    
    # Install dependencies
    if npm install; then
        log_success "React app dependencies installed"
        return 0
    else
        log_error "Failed to install React app dependencies"
        return 1
    fi
}

# Make scripts executable
setup_scripts() {
    log_info "Setting up scripts..."
    
    chmod +x "$SCRIPT_DIR/robot_bringup.sh"
    chmod +x "$SCRIPT_DIR/setup_fixed_ip.sh"
    
    log_success "Scripts are now executable"
}

# Create log directories
setup_directories() {
    log_info "Creating log directories..."
    
    mkdir -p "$WORKSPACE_DIR/logs"
    mkdir -p "/tmp/robot_bringup"
    
    log_success "Directories created"
}

# Install systemd service
install_service() {
    log_info "Installing systemd service..."
    
    local service_file="$SCRIPT_DIR/robot-bringup.service"
    local target_file="/etc/systemd/system/robot-bringup.service"
    
    if [ ! -f "$service_file" ]; then
        log_error "Service file not found: $service_file"
        return 1
    fi
    
    # Copy service file
    if sudo cp "$service_file" "$target_file"; then
        log_success "Service file copied to $target_file"
    else
        log_error "Failed to copy service file"
        return 1
    fi
    
    # Reload systemd
    if sudo systemctl daemon-reload; then
        log_success "Systemd daemon reloaded"
    else
        log_error "Failed to reload systemd daemon"
        return 1
    fi
    
    # Enable service
    if sudo systemctl enable robot-bringup.service; then
        log_success "Service enabled for auto-startup"
    else
        log_error "Failed to enable service"
        return 1
    fi
    
    log_info "Service installed successfully!"
    log_info "Use 'sudo systemctl start robot-bringup.service' to start now"
    log_info "Use 'sudo systemctl status robot-bringup.service' to check status"
    
    return 0
}

# Configure firewall
setup_firewall() {
    log_info "Configuring firewall..."
    
    # Check if ufw is active
    if sudo ufw status | grep -q "Status: active"; then
        log_info "UFW is active, adding firewall rules..."
        
        sudo ufw allow 3000/tcp comment "Robot Web Interface"
        sudo ufw allow 9090/tcp comment "ROS Bridge"
        
        log_success "Firewall rules added"
    else
        log_info "UFW is not active, skipping firewall configuration"
    fi
}

# Test installation
test_installation() {
    log_info "Testing installation..."
    
    # Test script execution
    if "$SCRIPT_DIR/robot_bringup.sh" status &> /dev/null; then
        log_success "Robot bringup script is working"
    else
        log_warning "Robot bringup script test failed (this is normal if services aren't running)"
    fi
    
    # Test React app
    local react_dir="$WORKSPACE_DIR/src/react-ros-app"
    if [ -d "$react_dir/node_modules" ]; then
        log_success "React app dependencies are installed"
    else
        log_warning "React app dependencies may not be properly installed"
    fi
    
    log_success "Installation test completed"
}

# Show post-installation instructions
show_instructions() {
    echo ""
    echo "=============================================="
    echo "🎉 Installation Complete!"
    echo "=============================================="
    echo ""
    echo "📋 Next Steps:"
    echo "==============="
    echo ""
    echo "1. 🌐 Configure Network (Recommended):"
    echo "   $SCRIPT_DIR/setup_fixed_ip.sh interactive"
    echo ""
    echo "2. 🚀 Start Robot Services:"
    echo "   $SCRIPT_DIR/robot_bringup.sh start"
    echo ""
    echo "3. 📊 Check Status:"
    echo "   $SCRIPT_DIR/robot_bringup.sh status"
    echo ""
    echo "4. 🔍 Monitor with Auto-restart:"
    echo "   $SCRIPT_DIR/robot_bringup.sh monitor"
    echo ""
    
    if [ -f "/etc/systemd/system/robot-bringup.service" ]; then
        echo "5. 🔧 System Service Commands:"
        echo "   sudo systemctl start robot-bringup.service    # Start now"
        echo "   sudo systemctl status robot-bringup.service   # Check status"
        echo "   sudo systemctl stop robot-bringup.service     # Stop service"
        echo ""
    fi
    
    echo "📖 Documentation:"
    echo "=================="
    echo "   $SCRIPT_DIR/README.md"
    echo ""
    echo "🆘 Help:"
    echo "========"
    echo "   $SCRIPT_DIR/robot_bringup.sh help"
    echo "   $SCRIPT_DIR/setup_fixed_ip.sh help"
    echo ""
}

# Main installation function
main() {
    echo "=============================================="
    echo "🤖 Robot Bringup Installation"
    echo "=============================================="
    echo ""
    
    local install_service=false
    local setup_ip=false
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --with-service)
                install_service=true
                shift
                ;;
            --with-fixed-ip)
                setup_ip=true
                shift
                ;;
            --help|-h)
                echo "Usage: $0 [OPTIONS]"
                echo ""
                echo "Options:"
                echo "  --with-service    Install as systemd service"
                echo "  --with-fixed-ip   Configure static IP address"
                echo "  --help, -h        Show this help"
                echo ""
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    # Run installation steps
    if ! check_prerequisites; then
        log_error "Prerequisites check failed"
        exit 1
    fi
    
    setup_directories
    setup_scripts
    
    if ! install_react_deps; then
        log_error "Failed to install React dependencies"
        exit 1
    fi
    
    setup_firewall
    
    if [ "$install_service" = true ]; then
        if ! install_service; then
            log_error "Failed to install systemd service"
            exit 1
        fi
    fi
    
    test_installation
    
    if [ "$setup_ip" = true ]; then
        echo ""
        log_info "Starting IP configuration..."
        "$SCRIPT_DIR/setup_fixed_ip.sh" interactive
    fi
    
    show_instructions
    
    log_success "Installation completed successfully!"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
    log_error "This script should not be run as root"
    log_info "Run as regular user. sudo will be used when needed."
    exit 1
fi

# Run main function
main "$@"
