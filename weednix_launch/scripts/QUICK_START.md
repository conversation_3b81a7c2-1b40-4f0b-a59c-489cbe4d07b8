# 🚀 Quick Start - Robot Bringup System

## ⚡ Instant Setup (3 Commands)

```bash
# 1. Install everything
cd ~/weednix_ws/src/weednix_launch/scripts
./install.sh --with-service --with-fixed-ip

# 2. Start robot services
 
# 3. Check status
./robot_bringup.sh status
```

## 🎯 What You Get

✅ **Automatic startup** of:
- ROS device1_bringup.launch
- ROS bridge server (port 9090)
- React web interface (port 3000)

✅ **Fixed IP address** for consistent network access

✅ **Auto-restart** on failures

✅ **System service** for boot-time startup

## 📱 Access Your Robot

After setup, access your robot at:
- **Web Interface**: `http://[ROBOT_IP]:3000`
- **ROS Bridge**: `ws://[ROBOT_IP]:9090`

## 🔧 Essential Commands

```bash
# Start services
./robot_bringup.sh start

# Start with monitoring (auto-restart)
./robot_bringup.sh monitor

# Check status
./robot_bringup.sh status

# Stop everything
./robot_bringup.sh stop

# View logs
./robot_bringup.sh logs
```

## 🌐 Network Setup

```bash
# Interactive IP configuration
./setup_fixed_ip.sh interactive

# Quick setup with specific IP
./setup_fixed_ip.sh *************

# Check current network
./setup_fixed_ip.sh show
```

## 🔄 System Service

```bash
# Start service now
sudo systemctl start robot-bringup.service

# Check service status
sudo systemctl status robot-bringup.service

# View service logs
sudo journalctl -u robot-bringup.service -f

# Stop service
sudo systemctl stop robot-bringup.service
```

## 🆘 Troubleshooting

### Services won't start?
```bash
# Check prerequisites
./robot_bringup.sh status

# View detailed logs
./robot_bringup.sh logs

# Check ports
sudo ss -tlnp | grep -E ':(3000|9090)'
```

### Can't access from phone?
```bash
# Check firewall
sudo ufw status
sudo ufw allow 3000
sudo ufw allow 9090

# Verify IP
./setup_fixed_ip.sh show
```

### Need to restart everything?
```bash
./robot_bringup.sh restart
```

## 📖 Full Documentation

For complete documentation, see:
- `README.md` - Comprehensive guide
- `robot_bringup.sh help` - Script help
- `setup_fixed_ip.sh help` - Network help

## 🎉 Success Indicators

When everything is working, you should see:
- ✅ All services showing "Running" in status
- 🌐 Web interface accessible from phone
- 🤖 Robot responding to commands
- 📊 No errors in logs

---

**Need help?** Check the troubleshooting section or review the logs for error messages.
