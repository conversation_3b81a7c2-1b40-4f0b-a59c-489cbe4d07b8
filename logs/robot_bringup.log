2025-06-01 05:00:11 - [0;34mINFO: Created log directory: /home/<USER>/weednix_ws/src/logs[0m
2025-06-01 05:00:11 - [0;34mINFO: Created PID directory: /tmp/robot_bringup[0m
2025-06-01 05:00:11 - [0;34mINFO: Checking prerequisites...[0m
2025-06-01 05:00:11 - [0;32mSUCCESS: All prerequisites checked[0m
2025-06-01 05:00:24 - [0;34mINFO: Created log directory: /home/<USER>/weednix_ws/src/logs[0m
2025-06-01 05:00:24 - [0;34mINFO: Created PID directory: /tmp/robot_bringup[0m
2025-06-01 05:00:24 - [0;34mINFO: Checking prerequisites...[0m
2025-06-01 05:00:24 - [0;32mSUCCESS: All prerequisites checked[0m
2025-06-01 05:00:47 - [0;34mINFO: Created log directory: /home/<USER>/weednix_ws/src/logs[0m
2025-06-01 05:00:47 - [0;34mINFO: Created PID directory: /tmp/robot_bringup[0m
2025-06-01 05:00:47 - [0;34mINFO: Checking prerequisites...[0m
2025-06-01 05:00:47 - [0;32mSUCCESS: All prerequisites checked[0m
2025-06-01 05:18:15 - [0;34mINFO: Created log directory: /home/<USER>/weednix_ws/src/logs[0m
2025-06-01 05:18:15 - [0;34mINFO: Created PID directory: /tmp/robot_bringup[0m
2025-06-01 05:18:15 - [0;34mINFO: Checking prerequisites...[0m
2025-06-01 05:18:15 - [0;32mSUCCESS: All prerequisites checked[0m
2025-06-01 05:20:13 - [0;34mINFO: Created log directory: /home/<USER>/weednix_ws/src/logs[0m
2025-06-01 05:20:13 - [0;34mINFO: Created PID directory: /tmp/robot_bringup[0m
2025-06-01 05:20:13 - [0;34mINFO: Checking prerequisites...[0m
2025-06-01 05:20:13 - [0;32mSUCCESS: All prerequisites checked[0m
2025-06-01 05:20:13 - [0;34mINFO: Starting robot bringup...[0m
2025-06-01 05:20:13 - [0;34mINFO: Starting robot bringup sequence...[0m
2025-06-01 05:20:13 - [0;34mINFO: Robot IP: ***********[0m
2025-06-01 05:20:13 - [0;34mINFO: Updating React app configuration with IP: ***********[0m
2025-06-01 05:20:13 - [0;32mSUCCESS: Updated .env file with robot IP: ***********[0m
2025-06-01 05:20:13 - [0;34mINFO: Starting ROS device1_bringup...[0m
2025-06-01 05:20:13 - [0;34mINFO: Sourced ROS Noetic: /opt/ros/noetic/setup.bash[0m
2025-06-01 05:20:18 - [0;31mERROR: ROS bringup failed to start[0m
2025-06-01 05:20:18 - [0;31mERROR: Failed to start ROS bringup[0m
2025-06-01 05:20:18 - [0;31mERROR: Robot bringup failed[0m
2025-06-01 05:20:50 - [0;34mINFO: Created log directory: /home/<USER>/weednix_ws/src/logs[0m
2025-06-01 05:20:50 - [0;34mINFO: Created PID directory: /tmp/robot_bringup[0m
2025-06-01 05:20:50 - [0;34mINFO: Checking prerequisites...[0m
2025-06-01 05:20:50 - [0;32mSUCCESS: All prerequisites checked[0m
2025-06-01 05:20:50 - [0;34mINFO: Starting robot bringup...[0m
2025-06-01 05:20:50 - [0;34mINFO: Starting robot bringup sequence...[0m
2025-06-01 05:20:50 - [0;34mINFO: Robot IP: ***********[0m
2025-06-01 05:20:50 - [0;34mINFO: Updating React app configuration with IP: ***********[0m
2025-06-01 05:20:50 - [0;32mSUCCESS: Updated .env file with robot IP: ***********[0m
2025-06-01 05:20:50 - [0;34mINFO: Starting ROS device1_bringup...[0m
2025-06-01 05:20:51 - [0;34mINFO: Sourced ROS Noetic: /opt/ros/noetic/setup.bash[0m
2025-06-01 05:20:56 - [0;31mERROR: ROS bringup failed to start[0m
2025-06-01 05:20:56 - [0;31mERROR: Failed to start ROS bringup[0m
2025-06-01 05:20:56 - [0;31mERROR: Robot bringup failed[0m
2025-06-01 05:21:11 - [0;34mINFO: Created log directory: /home/<USER>/weednix_ws/src/logs[0m
2025-06-01 05:21:11 - [0;34mINFO: Created PID directory: /tmp/robot_bringup[0m
2025-06-01 05:21:11 - [0;34mINFO: Checking prerequisites...[0m
2025-06-01 05:21:11 - [0;32mSUCCESS: All prerequisites checked[0m
2025-06-01 05:23:35 - [0;34mINFO: Created log directory: /home/<USER>/weednix_ws/src/logs[0m
2025-06-01 05:23:35 - [0;34mINFO: Created PID directory: /tmp/robot_bringup[0m
2025-06-01 05:23:35 - [0;34mINFO: Checking prerequisites...[0m
2025-06-01 05:23:35 - [0;32mSUCCESS: All prerequisites checked[0m
2025-06-01 05:23:35 - [0;34mINFO: Starting robot bringup...[0m
2025-06-01 05:23:35 - [0;34mINFO: Starting robot bringup sequence...[0m
2025-06-01 05:23:35 - [0;34mINFO: Robot IP: ***********[0m
2025-06-01 05:23:35 - [0;34mINFO: Updating React app configuration with IP: ***********[0m
2025-06-01 05:23:35 - [0;32mSUCCESS: Updated .env file with robot IP: ***********[0m
2025-06-01 05:23:35 - [0;34mINFO: Starting ROS device1_bringup...[0m
2025-06-01 05:23:35 - [0;34mINFO: Starting ROS master (roscore)...[0m
2025-06-01 05:23:35 - [0;34mINFO: Sourced ROS Noetic: /opt/ros/noetic/setup.bash[0m
2025-06-01 05:23:35 - [0;32mSUCCESS: ROS master started successfully[0m
2025-06-01 05:23:35 - [0;34mINFO: Sourced ROS Noetic: /opt/ros/noetic/setup.bash[0m
2025-06-01 05:23:40 - [0;31mERROR: ROS bringup failed to start[0m
2025-06-01 05:23:40 - [0;34mINFO: Check ROS log for details: /home/<USER>/weednix_ws/src/logs/ros_bringup.log[0m
2025-06-01 05:23:40 - [0;31mERROR: Failed to start ROS bringup[0m
2025-06-01 05:23:40 - [0;31mERROR: Robot bringup failed[0m
2025-06-01 05:25:39 - [0;34mINFO: Created log directory: /home/<USER>/weednix_ws/src/logs[0m
2025-06-01 05:25:39 - [0;34mINFO: Created PID directory: /tmp/robot_bringup[0m
2025-06-01 05:25:39 - [0;34mINFO: Checking prerequisites...[0m
2025-06-01 05:25:39 - [0;32mSUCCESS: All prerequisites checked[0m
2025-06-01 05:25:39 - [0;34mINFO: Starting robot bringup...[0m
2025-06-01 05:25:39 - [0;34mINFO: Starting robot bringup sequence...[0m
2025-06-01 05:25:39 - [0;34mINFO: Robot IP: ***********[0m
2025-06-01 05:25:39 - [0;34mINFO: Updating React app configuration with IP: ***********[0m
2025-06-01 05:25:39 - [0;32mSUCCESS: Updated .env file with robot IP: ***********[0m
2025-06-01 05:25:39 - [0;34mINFO: Starting ROS device1_bringup...[0m
2025-06-01 05:25:39 - [0;34mINFO: ROS master is already running[0m
2025-06-01 05:25:39 - [0;34mINFO: Sourced ROS Noetic: /opt/ros/noetic/setup.bash[0m
2025-06-01 05:25:39 - [1;33mWARNING: Workspace devel/setup.bash not found, trying to build workspace...[0m
2025-06-01 05:25:39 - [0;31mERROR: Failed to build workspace[0m
2025-06-01 05:25:39 - [0;31mERROR: Failed to start ROS bringup[0m
2025-06-01 05:25:39 - [0;31mERROR: Robot bringup failed[0m
